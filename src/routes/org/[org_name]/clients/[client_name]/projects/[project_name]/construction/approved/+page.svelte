<script lang="ts">
	import { goto } from '$app/navigation';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle,
	} from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { formatCurrency } from '$lib/utils';
	import { cn } from '$lib/utils';
	import { Calendar as CalendarIcon } from 'phosphor-svelte';
	import { Popover, PopoverContent, PopoverTrigger } from '$lib/components/ui/popover';
	import { Calendar } from '$lib/components/ui/calendar';
	import { format } from 'date-fns';
	import {
		DateFormatter,
		type DateValue,
		getLocalTimeZone,
		parseDate,
		today,
	} from '@internationalized/date';
	import * as Select from '$lib/components/ui/select';
	import * as Form from '$lib/components/ui/form';
	import { superForm } from 'sveltekit-superforms';

	const { data } = $props();

	// Extract data from props
	const { project, client, approvedChanges, wbsItems, budgetMap, filterForm } = data;

	// Setup filter form with Superforms
	const filterFormHandler = superForm(filterForm, {
		onUpdated({ form }) {
			// Build URL with filter parameters
			const params = new URLSearchParams();

			if (form.data.date_from) {
				params.set('date_from', form.data.date_from);
			}

			if (form.data.date_to) {
				params.set('date_to', form.data.date_to);
			}

			if (form.data.wbs_library_item_id) {
				params.set('wbs_item', form.data.wbs_library_item_id);
			}

			// Navigate to the filtered URL
			goto(`?${params.toString()}`);
		},
	});

	const { form: filterData, enhance: filterEnhance } = filterFormHandler;

	// Calculate total approved change amount
	const totalApprovedAmount = $derived(
		approvedChanges.reduce((sum, change) => sum + (change.potential_impact || 0), 0),
	);

	// Format date for display
	function formatDate(dateString: string | null) {
		if (!dateString) return '';
		return format(new Date(dateString), 'PPP');
	}

	// Get change owner display name
	function getChangeOwnerDisplay(change: (typeof approvedChanges)[0]) {
		if (change.risk_owner?.full_name) {
			return change.risk_owner.full_name;
		} else if (change.risk_owner?.email) {
			return change.risk_owner?.email;
		} else if (change.risk_owner_name) {
			return change.risk_owner_name;
		} else if (change.risk_owner_email) {
			return change.risk_owner_email;
		}
		return 'Not assigned';
	}

	// Get approved by display name
	function getApprovedByDisplay(change: (typeof approvedChanges)[0]) {
		if (change.approved_by?.full_name) {
			return change.approved_by.full_name;
		} else if (change.approved_by?.email) {
			return change.approved_by?.email;
		}
		return 'Unknown';
	}

	// Get original budget amount for WBS item
	function getOriginalBudgetAmount(wbsItemId: string | null) {
		if (!wbsItemId) return 0;
		return budgetMap[wbsItemId] || 0;
	}

	// Date formatter for displaying formatted dates
	const df = new DateFormatter('en-US', {
		dateStyle: 'long',
	});

	// State variables for date pickers
	const startDate = $derived($filterData.date_from ? parseDate($filterData.date_from) : undefined);
	const endDate = $derived($filterData.date_to ? parseDate($filterData.date_to) : undefined);

	// Set placeholder for calendar
	let placeholder = $state<DateValue>(today(getLocalTimeZone()));
</script>

<svelte:head>
	<title>Approved Changes - {project.name} - {client.name}</title>
</svelte:head>

<div class="container mx-auto py-6">
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">Approved Changes</h1>
			<p class="text-muted-foreground mt-2">
				Changes that have been approved and are being implemented
			</p>
		</div>
	</div>

	<!-- Filter Card -->
	<Card class="mb-6">
		<CardHeader>
			<CardTitle>Filters</CardTitle>
			<CardDescription>Filter approved changes by date or WBS item</CardDescription>
		</CardHeader>
		<CardContent>
			<form method="POST" use:filterEnhance>
				<div class="grid grid-cols-1 gap-4 md:grid-cols-4">
					<div>
						<Form.Field form={filterFormHandler} name="date_from">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>From Date</Form.Label>
									<Popover>
										<PopoverTrigger
											{...props}
											class={cn(
												buttonVariants({ variant: 'outline' }),
												'w-full justify-start text-left font-normal',
												!startDate && 'text-muted-foreground',
											)}
										>
											{startDate
												? df.format(startDate.toDate(getLocalTimeZone()))
												: 'Pick a start date'}
											<CalendarIcon class="ml-auto h-4 w-4 opacity-50" />
										</PopoverTrigger>
										<PopoverContent class="w-auto p-0" side="top">
											<Calendar
												type="single"
												value={startDate as DateValue}
												bind:placeholder
												calendarLabel="Start date"
												onValueChange={(v) => {
													if (v) {
														$filterData.date_from = v.toString();
													} else {
														$filterData.date_from = undefined;
													}
												}}
											/>
										</PopoverContent>
									</Popover>
									<Form.FieldErrors />
									<input hidden value={$filterData.date_from} name={props.name} />
								{/snippet}
							</Form.Control>
						</Form.Field>
					</div>

					<div>
						<Form.Field form={filterFormHandler} name="date_to">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>To Date</Form.Label>
									<Popover>
										<PopoverTrigger
											{...props}
											class={cn(
												buttonVariants({ variant: 'outline' }),
												'w-full justify-start text-left font-normal',
												!endDate && 'text-muted-foreground',
											)}
										>
											{endDate ? df.format(endDate.toDate(getLocalTimeZone())) : 'Pick an end date'}
											<CalendarIcon class="ml-auto h-4 w-4 opacity-50" />
										</PopoverTrigger>
										<PopoverContent class="w-auto p-0" side="top">
											<Calendar
												type="single"
												value={endDate as DateValue}
												bind:placeholder
												calendarLabel="End date"
												onValueChange={(v) => {
													if (v) {
														$filterData.date_to = v.toString();
													} else {
														$filterData.date_to = undefined;
													}
												}}
											/>
										</PopoverContent>
									</Popover>
									<Form.FieldErrors />
									<input hidden value={$filterData.date_to} name={props.name} />
								{/snippet}
							</Form.Control>
						</Form.Field>
					</div>

					<div>
						<Form.Field form={filterFormHandler} name="wbs_library_item_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>WBS Item</Form.Label>
									<Select.Root type="single" name={props.name}>
										<Select.Trigger {...props}>
											{$filterData.wbs_library_item_id
												? wbsItems.find(
														(item) => item.wbs_library_item_id === $filterData.wbs_library_item_id,
													)
													? `${wbsItems.find((item) => item.wbs_library_item_id === $filterData.wbs_library_item_id)?.code} - ${wbsItems.find((item) => item.wbs_library_item_id === $filterData.wbs_library_item_id)?.description}`
													: 'Select WBS item'
												: 'All WBS Items'}
										</Select.Trigger>
										<Select.Content>
											<Select.Item value="" label="All WBS Items" />
											{#each wbsItems as item (item.wbs_library_item_id)}
												<Select.Item
													value={item.wbs_library_item_id}
													label={`${item.code} - ${item.description}`}
												/>
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>

					<div class="flex items-end">
						<Button type="submit">Apply Filters</Button>
					</div>
				</div>
			</form>
		</CardContent>
	</Card>

	<!-- Summary Card -->
	<Card class="mb-6">
		<CardHeader>
			<CardTitle>Approved Changes Summary</CardTitle>
		</CardHeader>
		<CardContent>
			<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
				<div>
					<h3 class="text-lg font-medium">Total Approved Changes</h3>
					<p class="text-3xl font-bold">{approvedChanges.length}</p>
				</div>
				<div>
					<h3 class="text-lg font-medium">Total Approved Amount</h3>
					<p class="text-3xl font-bold">{formatCurrency(totalApprovedAmount)}</p>
				</div>
			</div>
		</CardContent>
	</Card>

	<!-- Approved Changes Table -->
	<div class="rounded-md border">
		<Table>
			<TableHeader>
				<TableRow>
					<TableHead>Title</TableHead>
					<TableHead>WBS Item</TableHead>
					<TableHead class="text-right">Original Budget</TableHead>
					<TableHead class="text-right">Change Amount</TableHead>
					<TableHead class="text-right">Date Approved</TableHead>
					<TableHead>Change Owner</TableHead>
					<TableHead>Approved By</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{#if approvedChanges.length === 0}
					<TableRow>
						<TableCell class="py-4 text-center" colspan={7}>No approved changes found</TableCell>
					</TableRow>
				{:else}
					{#each approvedChanges as change (change.approved_change_id)}
						<TableRow>
							<TableCell class="font-medium">
								<div>
									{change.title}
									<p class="text-muted-foreground max-w-60 truncate text-xs">
										{change.description}
									</p>
								</div>
							</TableCell>
							<TableCell
								>{change.wbs_item
									? `${change.wbs_item.code} - ${change.wbs_item.description}`
									: '-'}</TableCell
							>
							<TableCell class="text-right">
								{change.wbs_library_item_id
									? formatCurrency(getOriginalBudgetAmount(change.wbs_library_item_id))
									: '-'}
							</TableCell>
							<TableCell class="text-right">
								<Badge variant="secondary" class="font-bold">
									{change.potential_impact ? formatCurrency(change.potential_impact) : '-'}
								</Badge>
							</TableCell>
							<TableCell class="text-right">{formatDate(change.date_approved)}</TableCell>
							<TableCell>{getChangeOwnerDisplay(change)}</TableCell>
							<TableCell>{getApprovedByDisplay(change)}</TableCell>
						</TableRow>
					{/each}
				{/if}
			</TableBody>
		</Table>
	</div>
</div>
